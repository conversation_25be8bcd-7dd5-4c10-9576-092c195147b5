/* RTL Styles for Persian Scientific Database */
body {
    direction: rtl;
    text-align: right;
}

/* Reset text alignment for RTL */
.text-left { text-align: right !important; }
.text-right { text-align: left !important; }
.text-start { text-align: right !important; }
.text-end { text-align: left !important; }

/* Float adjustments */
.float-left { float: right !important; }
.float-right { float: left !important; }
.float-start { float: right !important; }
.float-end { float: left !important; }

/* Margin and Padding RTL adjustments */
.ml-auto { margin-right: auto !important; margin-left: unset !important; }
.mr-auto { margin-left: auto !important; margin-right: unset !important; }
.ms-auto { margin-right: auto !important; margin-left: unset !important; }
.me-auto { margin-left: auto !important; margin-right: unset !important; }

/* Positioning adjustments */
.left-0 { right: 0 !important; left: unset !important; }
.right-0 { left: 0 !important; right: unset !important; }

/* Transform adjustments for animations */
.tp-fade-anim[data-fade-from="left"] { transform: translateX(50px) !important; }
.tp-fade-anim[data-fade-from="right"] { transform: translateX(-50px) !important; }

/* Header RTL adjustments */
.tp-header-area .tp-header-logo {
    text-align: right;
}

.tp-main-menu ul li {
    float: right;
}

.tp-main-menu ul li:first-child {
    margin-right: 0;
    margin-left: 40px;
}

.tp-main-menu ul li:last-child {
    margin-left: 0;
    margin-right: 40px;
}

/* Dropdown menu RTL */
.tp-submenu {
    right: 0;
    left: unset;
    text-align: right;
}

.tp-megamenu {
    right: 0;
    left: unset;
}

/* Button and icon RTL adjustments */
.tp-btn-xl .btn-icon {
    margin-right: 10px;
    margin-left: 0;
}

.tp-hero-video-btn {
    margin-left: 20px;
    margin-right: 0;
}

/* Service items RTL */
.tp-service-item ul li::before {
    right: 0;
    left: unset;
}

/* Footer RTL adjustments */
.tp-footer-social ul li {
    float: right;
    margin-left: 30px;
    margin-right: 0;
}

.tp-footer-social ul li:first-child {
    margin-left: 0;
}

/* Scroll animations RTL fix */
.moving-text .wrapper-text {
    direction: rtl;
}

/* Form elements RTL */
input, textarea, select {
    text-align: right;
    direction: rtl;
}

/* Search form RTL */
.tp-search-form-input input {
    text-align: right;
    padding-right: 20px;
    padding-left: 60px;
}

.tp-search-form-icon {
    right: unset;
    left: 20px;
}

/* Mobile menu RTL */
.tp-offcanvas {
    right: -100%;
    left: unset;
}

.tp-offcanvas.opened {
    right: 0;
}

/* Counter RTL adjustments */
.tp-counter-wrap-box {
    direction: rtl;
}

/* Portfolio RTL adjustments */
.tp-portfolio-btn {
    text-align: center;
}

/* Blog RTL adjustments */
.tp-blog-item {
    text-align: right;
}

/* Responsive RTL adjustments */
@media (max-width: 991px) {
    .tp-main-menu ul li {
        float: none;
        text-align: right;
    }
    
    .tp-header-logo {
        text-align: right;
    }
}

@media (max-width: 767px) {
    .tp-hero-content {
        text-align: center;
    }
    
    .tp-service-item {
        text-align: right;
    }
}