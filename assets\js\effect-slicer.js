/**
 * UI Initiative Slicer Slider
 *
 * Images slicer slider
 *
 * https://uiinitiative.com
 *
 * Copyright 2024 UI Initiative
 *
 * Released under the UI Initiative Regular License
 *
 * June 08, 2024
 *
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).EffectSlicer=t()}(this,(function(){"use strict";return"undefined"!=typeof window&&window.SwiperElementRegisterParams&&window.SwiperElementRegisterParams(["slicerEffect"]),function({swiper:e,extendParams:t,on:s,emit:i}){t({slicerEffect:{split:5}});let r=!1;const n=()=>{e.slides.forEach((t=>{if(t.querySelector(".swiper-slicer-image-clones"))return;const s=t.querySelector(".swiper-slicer-image");if(!s)return;const i=s.nextElementSibling,r=document.createElement("div");r.classList.add("swiper-slicer-image-clones");for(let t=0;t<e.params.slicerEffect.split;t+=1){const e=document.createElement("div");e.classList.add("swiper-slicer-image-clone"),e.appendChild(s.cloneNode()),r.appendChild(e)}i?s.parentNode.insertBefore(r,i):s.parentNode.appendChild(r)}))},l=()=>{e.slides.forEach((t=>t.querySelectorAll(".swiper-slicer-image").forEach((t=>{t.style.width=`${e.width}px`,t.style.height=`${e.height}px`})))),e.slides.forEach((t=>{t.querySelectorAll(".swiper-slicer-image-clone").forEach(((t,s)=>{const i=t.querySelector(".swiper-slicer-image");"horizontal"===e.params.direction?(t.style.height=100/e.params.slicerEffect.split+"%",t.style.top=100/e.params.slicerEffect.split*s+"%",i.style.top=`-${100*s}%`):(t.style.width=100/e.params.slicerEffect.split+"%",t.style.left=100/e.params.slicerEffect.split*s+"%",i.style.left=`-${100*s}%`)}))}))},a=t=>{e.slides.forEach((s=>{const i=s.querySelectorAll(".swiper-slicer-image-clone"),n=s.querySelector(".swiper-slide-content");n&&(n.style.transitionDuration=`${t}ms`),i.forEach(((s,n)=>{0===t?(s.style.transitionTimingFunction="ease-out",s.style.transitionDuration=r?"0ms":`${e.params.speed+e.params.speed/(i.length-1)*(i.length-n-1)}ms`):(s.style.transitionTimingFunction="",s.style.transitionDuration=`${t+t/(i.length-1)*(i.length-n-1)}ms`)}))})),function({duration:t}){const{slides:s,activeIndex:i}=e;if(0!==t){let t=!1;const r=s[i].querySelector(".swiper-slicer-image-clone:nth-child(1)");if(!r)return;const n=s=>{if(s.target!==r)return;if(r.removeEventListener("transitionend",n),t)return;if(!e||e.destroyed)return;t=!0,e.animating=!1;const i=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0});e.wrapperEl.dispatchEvent(i)};r.addEventListener("transitionend",n)}}({duration:t})};s("beforeLoopFix",(()=>{r=!0})),s("loopFix",(()=>{r=!1})),s("setTranslate",(()=>{"slicer"===e.params.effect&&(()=>{const t="vertical"===e.params.direction?"Y":"X";e.slides.forEach(((s,i)=>{s.style.transform=`translate${t}(-${100*i}%)`;const r=s.progress,n=s.querySelector(".swiper-slide-content");n&&(n.style.transform=`translate${t}(${e.size*-r*1.2}px)`),s.querySelectorAll(".swiper-slicer-image-clone").forEach((e=>{const s=-r;e.style.transform=`translate${t}(${100*s}%)`}))}))})()})),s("setTransition",((t,s)=>{"slicer"===e.params.effect&&a(s)})),s("slidesLengthChange",(()=>{n(),l()})),s("beforeInit",(()=>{if("slicer"!==e.params.effect)return;e.classNames.push("swiper-slicer");const t={slidesPerView:1,slidesPerGroup:1,watchSlidesProgress:!0,spaceBetween:0,virtualTranslate:!0};Object.assign(e.params,t),Object.assign(e.originalParams,t)})),s("init",(()=>{"slicer"===e.params.effect&&(n(),i("setTranslate",e,e.translate))})),s("resize init",(()=>{"slicer"===e.params.effect&&l()}))}}));
//# sourceMappingURL=effect-slicer.min.js.map