/* Custom Hero Section Styles */

/* Search Section Styling */
.tp-hero-search-section {
    margin-top: 60px;
    padding: 40px 20px;
    background: linear-gradient(135deg, rgba(196, 238, 24, 0.1), rgba(196, 238, 24, 0.05));
    border-radius: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(196, 238, 24, 0.2);
}

.tp-hero-search-input {
    background: #ffffff !important;
    border: 3px solid #C4EE18 !important;
    border-radius: 50px !important;
    padding: 25px 70px 25px 35px !important;
    font-size: 20px !important;
    font-family: 'Vazirmatn', sans-serif !important;
    color: #333 !important;
    box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1) !important;
    transition: all 0.4s ease !important;
    min-height: 80px !important;
    font-weight: 500 !important;
}

.tp-hero-search-input:focus {
    outline: none !important;
    border-color: #a8d916 !important;
    box-shadow: 0 0 50px rgba(196, 238, 24, 0.8), 0 20px 50px rgba(0, 0, 0, 0.2) !important;
    transform: translateY(-3px) !important;
    background: #fefffe !important;
}

.tp-hero-search-input::placeholder {
    color: #666 !important;
    font-size: 18px !important;
    font-weight: 400 !important;
}

.tp-search-form-icon {
    right: 15px !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    background: linear-gradient(135deg, #C4EE18, #a8d916) !important;
    border: none !important;
    border-radius: 50% !important;
    width: 60px !important;
    height: 60px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    cursor: pointer !important;
    transition: all 0.4s ease !important;
    box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3) !important;
    z-index: 10 !important;
}

.tp-search-form-icon:hover {
    background: linear-gradient(135deg, #a8d916, #95c214) !important;
    box-shadow: 0 12px 35px rgba(196, 238, 24, 0.7), 0 0 30px rgba(196, 238, 24, 0.5) !important;
    transform: translateY(-50%) scale(1.1) !important;
}

.tp-search-form-icon svg {
    color: #333 !important;
    width: 24px !important;
    height: 24px !important;
}

/* Glowing effect animation */
@keyframes glow {
    0% {
        box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: #C4EE18;
    }
    50% {
        box-shadow: 0 20px 60px rgba(196, 238, 24, 0.6), 0 10px 25px rgba(0, 0, 0, 0.15);
        border-color: #a8d916;
    }
    100% {
        box-shadow: 0 15px 40px rgba(196, 238, 24, 0.3), 0 5px 15px rgba(0, 0, 0, 0.1);
        border-color: #C4EE18;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 12px 35px rgba(196, 238, 24, 0.7), 0 0 30px rgba(196, 238, 24, 0.5);
    }
    100% {
        transform: scale(1);
        box-shadow: 0 8px 25px rgba(196, 238, 24, 0.5), 0 0 20px rgba(196, 238, 24, 0.3);
    }
}

.tp-search-form-input {
    animation: glow 4s ease-in-out infinite !important;
}

.tp-search-form-icon {
    animation: pulse 3s ease-in-out infinite !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    .tp-hero-search-input {
        padding: 15px 50px 15px 20px;
        font-size: 16px;
        min-height: 60px;
    }
    
    .tp-search-form-icon {
        width: 40px;
        height: 40px;
        right: 15px;
    }
    
    .tp-search-form-icon svg {
        width: 16px;
        height: 16px;
    }
}

@media (max-width: 576px) {
    .tp-hero-search-section {
        margin-top: 40px;
    }
    
    .tp-hero-search-input {
        padding: 12px 45px 12px 15px;
        font-size: 14px;
        min-height: 50px;
    }
    
    .tp-search-form-icon {
        width: 35px;
        height: 35px;
        right: 10px;
    }
    
    .tp-search-form-icon svg {
        width: 14px;
        height: 14px;
    }
}

/* Hero Title Centering */
.tp-hero-title.text-center {
    text-align: center !important;
}

/* Hero Bottom Content Centering */
.tp-hero-bottom-content.text-center {
    text-align: center !important;
}

.tp-hero-bottom-content.text-center .tp-hero-customer {
    justify-content: center !important;
}

/* Additional Search Form Styling */
.tp-search-form {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.tp-search-form::before {
    content: '';
    position: absolute;
    top: -10px;
    left: -10px;
    right: -10px;
    bottom: -10px;
    background: linear-gradient(45deg, #C4EE18, #a8d916, #C4EE18);
    border-radius: 60px;
    z-index: -1;
    opacity: 0.3;
    animation: rotate 6s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Search section title */
.tp-hero-search-section::before {
    content: '🔍 جستجو کنید';
    display: block;
    text-align: center;
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin-bottom: 20px;
    font-family: 'Vazirmatn', sans-serif;
}
